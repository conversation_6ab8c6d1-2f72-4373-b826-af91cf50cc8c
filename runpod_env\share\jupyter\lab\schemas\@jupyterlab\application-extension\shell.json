{"$schema": "http://json-schema.org/draft-07/schema", "title": "JupyterLab Shell", "description": "JupyterLab Shell layout settings.", "jupyter.lab.menus": {"context": [{"command": "sidebar:switch", "selector": ".jp-SideBar .lm-TabBar-tab", "rank": 500}]}, "properties": {"hiddenMode": {"type": "string", "title": "Hidden mode of main panel widgets", "description": "The method for hiding widgets in the main dock panel. Using `scale` will increase performance on Firefox but don't use it with Chrome, Chromium or Edge. Similar performance gains are seen with `contentVisibility` which is only available in Chromium-based browsers.", "enum": ["display", "scale", "contentVisibility"], "default": "display"}, "startMode": {"enum": ["", "single", "multiple"], "title": "Start mode: ``, `single` or `multiple`", "description": "The mode under which <PERSON><PERSON><PERSON><PERSON><PERSON> should start. If empty, the mode will be imposed by the URL", "default": ""}, "layout": {"type": "object", "title": "Customize shell widget positioning", "description": "Overrides default widget position in the application layout\ne.g. to position terminals in the right sidebar in multiple documents mode and in the down are in single document mode, {\n  \"single\": { \"Terminal\": { \"area\": \"down\" } },\n  \"multiple\": { \"Terminal\": { \"area\": \"right\" } }\n}.", "properties": {"single": {"$ref": "#/definitions/layout", "default": {"Linked Console": {"area": "down"}, "Inspector": {"area": "down"}, "Cloned Output": {"area": "down"}}}, "multiple": {"$ref": "#/definitions/layout", "default": {}}}, "default": {"single": {"Linked Console": {"area": "down"}, "Inspector": {"area": "down"}, "Cloned Output": {"area": "down"}}, "multiple": {}}, "additionalProperties": false}}, "additionalProperties": false, "type": "object", "definitions": {"layout": {"type": "object", "properties": {"[\\w-]+": {"type": "object", "properties": {"area": {"enum": ["main", "left", "right", "down"]}, "options": {"$ref": "#/definitions/options"}}, "additionalProperties": false}}}, "options": {"type": "object", "properties": {"mode": {"type": "string", "enum": ["split-top", "split-left", "split-right", "split-bottom", "tab-before", "tab-after"]}, "rank": {"type": "number", "minimum": 0}, "ref": {"type": "string", "minLength": 1}}}}}