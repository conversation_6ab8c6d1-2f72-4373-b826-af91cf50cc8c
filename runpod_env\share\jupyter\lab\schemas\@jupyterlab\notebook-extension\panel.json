{"title": "Notebook Panel", "description": "Notebook Panel settings.", "jupyter.lab.toolbars": {"Notebook": [{"name": "save", "rank": 10}, {"name": "insert", "command": "notebook:insert-cell-below", "icon": "ui-components:add", "rank": 20}, {"name": "cut", "command": "notebook:cut-cell", "rank": 21}, {"name": "copy", "command": "notebook:copy-cell", "rank": 22}, {"name": "paste", "command": "notebook:paste-cell-below", "rank": 23}, {"name": "run", "command": "notebook:run-cell-and-select-next", "rank": 30}, {"name": "interrupt", "command": "notebook:interrupt-kernel", "rank": 31}, {"name": "restart", "command": "notebook:restart-kernel", "rank": 32}, {"name": "restart-and-run", "command": "notebook:restart-run-all", "rank": 33}, {"name": "cellType", "rank": 40}, {"name": "spacer", "type": "spacer", "rank": 100}, {"name": "kernelName", "rank": 1000}, {"name": "executionProgress", "rank": 1002}, {"name": "scrollbar", "command": "notebook:toggle-virtual-scrollbar", "rank": 1003}]}, "jupyter.lab.transform": true, "properties": {"toolbar": {"title": "Notebook panel toolbar items", "description": "Note: To disable a toolbar item,\ncopy it to User Preferences and add the\n\"disabled\" key. The following example will disable the Interrupt button item:\n{\n  \"toolbar\": [\n    {\n      \"name\": \"interrupt\",\n      \"disabled\": true\n    }\n  ]\n}\n\nToolbar description:", "items": {"$ref": "#/definitions/toolbarItem"}, "type": "array", "default": []}}, "additionalProperties": false, "type": "object", "definitions": {"toolbarItem": {"properties": {"name": {"title": "Unique name", "type": "string"}, "args": {"title": "Command arguments", "type": "object"}, "command": {"title": "Command id", "type": "string", "default": ""}, "disabled": {"title": "Whether the item is ignored or not", "type": "boolean", "default": false}, "icon": {"title": "Item icon id", "description": "If defined, it will override the command icon", "type": "string"}, "label": {"title": "Item label", "description": "If defined, it will override the command label", "type": "string"}, "caption": {"title": "Item caption", "description": "If defined, it will override the command caption", "type": "string"}, "type": {"title": "Item type", "type": "string", "enum": ["command", "spacer"]}, "rank": {"title": "Item rank", "type": "number", "minimum": 0, "default": 50}}, "required": ["name"], "additionalProperties": false, "type": "object"}}}