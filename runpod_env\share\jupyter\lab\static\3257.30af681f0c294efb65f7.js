/*! For license information please see 3257.30af681f0c294efb65f7.js.LICENSE.txt */
(self["webpackChunk_jupyterlab_application_top"]=self["webpackChunk_jupyterlab_application_top"]||[]).push([[3257],{23257:(e,r,t)=>{e.exports=function(e){var r={};function t(n){if(r[n])return r[n].exports;var a=r[n]={exports:{},id:n,loaded:false};e[n].call(a.exports,a,a.exports,t);a.loaded=true;return a.exports}t.m=e;t.c=r;t.p="";return t(0)}([function(e,r,t){e.exports=t(1)},function(e,r,t){"use strict";Object.defineProperty(r,"__esModule",{value:true});function n(e){return e&&e.__esModule?e:{default:e}}var a=t(2);var i=n(a);r["default"]=i["default"];e.exports=r["default"]},function(e,r,t){"use strict";Object.defineProperty(r,"__esModule",{value:true});var n=Object.assign||function(e){for(var r=1;r<arguments.length;r++){var t=arguments[r];for(var n in t){if(Object.prototype.hasOwnProperty.call(t,n)){e[n]=t[n]}}}return e};r["default"]=p;function a(e){return e&&e.__esModule?e:{default:e}}function i(e,r){var t={};for(var n in e){if(r.indexOf(n)>=0)continue;if(!Object.prototype.hasOwnProperty.call(e,n))continue;t[n]=e[n]}return t}var u=t(3);var o=t(4);var s=a(o);var f=t(14);var l=t(15);var c=a(l);p.propTypes={activeClassName:s["default"].string,activeIndex:s["default"].number,activeStyle:s["default"].object,autoEscape:s["default"].bool,className:s["default"].string,findChunks:s["default"].func,highlightClassName:s["default"].oneOfType([s["default"].object,s["default"].string]),highlightStyle:s["default"].object,highlightTag:s["default"].oneOfType([s["default"].node,s["default"].func,s["default"].string]),sanitize:s["default"].func,searchWords:s["default"].arrayOf(s["default"].oneOfType([s["default"].string,s["default"].instanceOf(RegExp)])).isRequired,textToHighlight:s["default"].string.isRequired,unhighlightTag:s["default"].oneOfType([s["default"].node,s["default"].func,s["default"].string]),unhighlightClassName:s["default"].string,unhighlightStyle:s["default"].object};function p(e){var r=e.activeClassName;var t=r===undefined?"":r;var a=e.activeIndex;var o=a===undefined?-1:a;var s=e.activeStyle;var l=e.autoEscape;var p=e.caseSensitive;var d=p===undefined?false:p;var v=e.className;var h=e.findChunks;var y=e.highlightClassName;var g=y===undefined?"":y;var m=e.highlightStyle;var b=m===undefined?{}:m;var O=e.highlightTag;var x=O===undefined?"mark":O;var w=e.sanitize;var T=e.searchWords;var E=e.textToHighlight;var j=e.unhighlightTag;var k=j===undefined?"span":j;var N=e.unhighlightClassName;var _=N===undefined?"":N;var S=e.unhighlightStyle;var P=i(e,["activeClassName","activeIndex","activeStyle","autoEscape","caseSensitive","className","findChunks","highlightClassName","highlightStyle","highlightTag","sanitize","searchWords","textToHighlight","unhighlightTag","unhighlightClassName","unhighlightStyle"]);var C=(0,u.findAll)({autoEscape:l,caseSensitive:d,findChunks:h,sanitize:w,searchWords:T,textToHighlight:E});var I=x;var R=-1;var A="";var D=undefined;var q=function e(r){var t={};for(var n in r){t[n.toLowerCase()]=r[n]}return t};var L=(0,c["default"])(q);return(0,f.createElement)("span",n({className:v},P,{children:C.map((function(e,r){var n=E.substr(e.start,e.end-e.start);if(e.highlight){R++;var a=undefined;if(typeof g==="object"){if(!d){g=L(g);a=g[n.toLowerCase()]}else{a=g[n]}}else{a=g}var i=R===+o;A=a+" "+(i?t:"");D=i===true&&s!=null?Object.assign({},b,s):b;var u={children:n,className:A,key:r,style:D};if(typeof I!=="string"){u.highlightIndex=R}return(0,f.createElement)(I,u)}else{return(0,f.createElement)(k,{children:n,className:_,key:r,style:S})}}))}))}e.exports=r["default"]},function(e,r){e.exports=function(e){var r={};function t(n){if(r[n])return r[n].exports;var a=r[n]={exports:{},id:n,loaded:false};e[n].call(a.exports,a,a.exports,t);a.loaded=true;return a.exports}t.m=e;t.c=r;t.p="";return t(0)}([function(e,r,t){e.exports=t(1)},function(e,r,t){"use strict";Object.defineProperty(r,"__esModule",{value:true});var n=t(2);Object.defineProperty(r,"combineChunks",{enumerable:true,get:function e(){return n.combineChunks}});Object.defineProperty(r,"fillInChunks",{enumerable:true,get:function e(){return n.fillInChunks}});Object.defineProperty(r,"findAll",{enumerable:true,get:function e(){return n.findAll}});Object.defineProperty(r,"findChunks",{enumerable:true,get:function e(){return n.findChunks}})},function(e,r){"use strict";Object.defineProperty(r,"__esModule",{value:true});var t=r.findAll=function e(r){var t=r.autoEscape,u=r.caseSensitive,o=u===undefined?false:u,s=r.findChunks,f=s===undefined?a:s,l=r.sanitize,c=r.searchWords,p=r.textToHighlight;return i({chunksToHighlight:n({chunks:f({autoEscape:t,caseSensitive:o,sanitize:l,searchWords:c,textToHighlight:p})}),totalLength:p?p.length:0})};var n=r.combineChunks=function e(r){var t=r.chunks;t=t.sort((function(e,r){return e.start-r.start})).reduce((function(e,r){if(e.length===0){return[r]}else{var t=e.pop();if(r.start<=t.end){var n=Math.max(t.end,r.end);e.push({start:t.start,end:n})}else{e.push(t,r)}return e}}),[]);return t};var a=function e(r){var t=r.autoEscape,n=r.caseSensitive,a=r.sanitize,i=a===undefined?u:a,s=r.searchWords,f=r.textToHighlight;f=i(f);return s.filter((function(e){return e})).reduce((function(e,r){r=i(r);if(t){r=o(r)}var a=new RegExp(r,n?"g":"gi");var u=void 0;while(u=a.exec(f)){var s=u.index;var l=a.lastIndex;if(l>s){e.push({start:s,end:l})}if(u.index==a.lastIndex){a.lastIndex++}}return e}),[])};r.findChunks=a;var i=r.fillInChunks=function e(r){var t=r.chunksToHighlight,n=r.totalLength;var a=[];var i=function e(r,t,n){if(t-r>0){a.push({start:r,end:t,highlight:n})}};if(t.length===0){i(0,n,false)}else{var u=0;t.forEach((function(e){i(u,e.start,false);i(e.start,e.end,true);u=e.end}));i(u,n,false)}return a};function u(e){return e}function o(e){return e.replace(/[\-\[\]\/\{\}\(\)\*\+\?\.\\\^\$\|]/g,"\\$&")}}])},function(e,r,t){(function(r){if(r.env.NODE_ENV!=="production"){var n=typeof Symbol==="function"&&Symbol.for&&Symbol.for("react.element")||60103;var a=function(e){return typeof e==="object"&&e!==null&&e.$$typeof===n};var i=true;e.exports=t(6)(a,i)}else{e.exports=t(13)()}}).call(r,t(5))},function(e,r){var t=e.exports={};var n;var a;function i(){throw new Error("setTimeout has not been defined")}function u(){throw new Error("clearTimeout has not been defined")}(function(){try{if(typeof setTimeout==="function"){n=setTimeout}else{n=i}}catch(e){n=i}try{if(typeof clearTimeout==="function"){a=clearTimeout}else{a=u}}catch(e){a=u}})();function o(e){if(n===setTimeout){return setTimeout(e,0)}if((n===i||!n)&&setTimeout){n=setTimeout;return setTimeout(e,0)}try{return n(e,0)}catch(r){try{return n.call(null,e,0)}catch(r){return n.call(this,e,0)}}}function s(e){if(a===clearTimeout){return clearTimeout(e)}if((a===u||!a)&&clearTimeout){a=clearTimeout;return clearTimeout(e)}try{return a(e)}catch(r){try{return a.call(null,e)}catch(r){return a.call(this,e)}}}var f=[];var l=false;var c;var p=-1;function d(){if(!l||!c){return}l=false;if(c.length){f=c.concat(f)}else{p=-1}if(f.length){v()}}function v(){if(l){return}var e=o(d);l=true;var r=f.length;while(r){c=f;f=[];while(++p<r){if(c){c[p].run()}}p=-1;r=f.length}c=null;l=false;s(e)}t.nextTick=function(e){var r=new Array(arguments.length-1);if(arguments.length>1){for(var t=1;t<arguments.length;t++){r[t-1]=arguments[t]}}f.push(new h(e,r));if(f.length===1&&!l){o(v)}};function h(e,r){this.fun=e;this.array=r}h.prototype.run=function(){this.fun.apply(null,this.array)};t.title="browser";t.browser=true;t.env={};t.argv=[];t.version="";t.versions={};function y(){}t.on=y;t.addListener=y;t.once=y;t.off=y;t.removeListener=y;t.removeAllListeners=y;t.emit=y;t.prependListener=y;t.prependOnceListener=y;t.listeners=function(e){return[]};t.binding=function(e){throw new Error("process.binding is not supported")};t.cwd=function(){return"/"};t.chdir=function(e){throw new Error("process.chdir is not supported")};t.umask=function(){return 0}},function(e,r,t){(function(r){"use strict";var n=t(7);var a=t(8);var i=t(9);var u=t(10);var o=t(11);var s=t(12);e.exports=function(e,t){var f=typeof Symbol==="function"&&Symbol.iterator;var l="@@iterator";function c(e){var r=e&&(f&&e[f]||e[l]);if(typeof r==="function"){return r}}var p="<<anonymous>>";var d={array:g("array"),bool:g("boolean"),func:g("function"),number:g("number"),object:g("object"),string:g("string"),symbol:g("symbol"),any:m(),arrayOf:b,element:O(),instanceOf:x,node:j(),objectOf:T,oneOf:w,oneOfType:E,shape:k,exact:N};function v(e,r){if(e===r){return e!==0||1/e===1/r}else{return e!==e&&r!==r}}function h(e){this.message=e;this.stack=""}h.prototype=Error.prototype;function y(e){if(r.env.NODE_ENV!=="production"){var n={};var u=0}function s(s,f,l,c,d,v,y){c=c||p;v=v||l;if(y!==o){if(t){a(false,"Calling PropTypes validators directly is not supported by the `prop-types` package. "+"Use `PropTypes.checkPropTypes()` to call them. "+"Read more at http://fb.me/use-check-prop-types")}else if(r.env.NODE_ENV!=="production"&&typeof console!=="undefined"){var g=c+":"+l;if(!n[g]&&u<3){i(false,"You are manually calling a React.PropTypes validation "+"function for the `%s` prop on `%s`. This is deprecated "+"and will throw in the standalone `prop-types` package. "+"You may be seeing this warning due to a third-party PropTypes "+"library. See https://fb.me/react-warning-dont-call-proptypes "+"for details.",v,c);n[g]=true;u++}}}if(f[l]==null){if(s){if(f[l]===null){return new h("The "+d+" `"+v+"` is marked as required "+("in `"+c+"`, but its value is `null`."))}return new h("The "+d+" `"+v+"` is marked as required in "+("`"+c+"`, but its value is `undefined`."))}return null}else{return e(f,l,c,d,v)}}var f=s.bind(null,false);f.isRequired=s.bind(null,true);return f}function g(e){function r(r,t,n,a,i,u){var o=r[t];var s=P(o);if(s!==e){var f=C(o);return new h("Invalid "+a+" `"+i+"` of type "+("`"+f+"` supplied to `"+n+"`, expected ")+("`"+e+"`."))}return null}return y(r)}function m(){return y(n.thatReturnsNull)}function b(e){function r(r,t,n,a,i){if(typeof e!=="function"){return new h("Property `"+i+"` of component `"+n+"` has invalid PropType notation inside arrayOf.")}var u=r[t];if(!Array.isArray(u)){var s=P(u);return new h("Invalid "+a+" `"+i+"` of type "+("`"+s+"` supplied to `"+n+"`, expected an array."))}for(var f=0;f<u.length;f++){var l=e(u,f,n,a,i+"["+f+"]",o);if(l instanceof Error){return l}}return null}return y(r)}function O(){function r(r,t,n,a,i){var u=r[t];if(!e(u)){var o=P(u);return new h("Invalid "+a+" `"+i+"` of type "+("`"+o+"` supplied to `"+n+"`, expected a single ReactElement."))}return null}return y(r)}function x(e){function r(r,t,n,a,i){if(!(r[t]instanceof e)){var u=e.name||p;var o=R(r[t]);return new h("Invalid "+a+" `"+i+"` of type "+("`"+o+"` supplied to `"+n+"`, expected ")+("instance of `"+u+"`."))}return null}return y(r)}function w(e){if(!Array.isArray(e)){r.env.NODE_ENV!=="production"?i(false,"Invalid argument supplied to oneOf, expected an instance of array."):void 0;return n.thatReturnsNull}function t(r,t,n,a,i){var u=r[t];for(var o=0;o<e.length;o++){if(v(u,e[o])){return null}}var s=JSON.stringify(e);return new h("Invalid "+a+" `"+i+"` of value `"+u+"` "+("supplied to `"+n+"`, expected one of "+s+"."))}return y(t)}function T(e){function r(r,t,n,a,i){if(typeof e!=="function"){return new h("Property `"+i+"` of component `"+n+"` has invalid PropType notation inside objectOf.")}var u=r[t];var s=P(u);if(s!=="object"){return new h("Invalid "+a+" `"+i+"` of type "+("`"+s+"` supplied to `"+n+"`, expected an object."))}for(var f in u){if(u.hasOwnProperty(f)){var l=e(u,f,n,a,i+"."+f,o);if(l instanceof Error){return l}}}return null}return y(r)}function E(e){if(!Array.isArray(e)){r.env.NODE_ENV!=="production"?i(false,"Invalid argument supplied to oneOfType, expected an instance of array."):void 0;return n.thatReturnsNull}for(var t=0;t<e.length;t++){var a=e[t];if(typeof a!=="function"){i(false,"Invalid argument supplied to oneOfType. Expected an array of check functions, but "+"received %s at index %s.",I(a),t);return n.thatReturnsNull}}function u(r,t,n,a,i){for(var u=0;u<e.length;u++){var s=e[u];if(s(r,t,n,a,i,o)==null){return null}}return new h("Invalid "+a+" `"+i+"` supplied to "+("`"+n+"`."))}return y(u)}function j(){function e(e,r,t,n,a){if(!_(e[r])){return new h("Invalid "+n+" `"+a+"` supplied to "+("`"+t+"`, expected a ReactNode."))}return null}return y(e)}function k(e){function r(r,t,n,a,i){var u=r[t];var s=P(u);if(s!=="object"){return new h("Invalid "+a+" `"+i+"` of type `"+s+"` "+("supplied to `"+n+"`, expected `object`."))}for(var f in e){var l=e[f];if(!l){continue}var c=l(u,f,n,a,i+"."+f,o);if(c){return c}}return null}return y(r)}function N(e){function r(r,t,n,a,i){var s=r[t];var f=P(s);if(f!=="object"){return new h("Invalid "+a+" `"+i+"` of type `"+f+"` "+("supplied to `"+n+"`, expected `object`."))}var l=u({},r[t],e);for(var c in l){var p=e[c];if(!p){return new h("Invalid "+a+" `"+i+"` key `"+c+"` supplied to `"+n+"`."+"\nBad object: "+JSON.stringify(r[t],null,"  ")+"\nValid keys: "+JSON.stringify(Object.keys(e),null,"  "))}var d=p(s,c,n,a,i+"."+c,o);if(d){return d}}return null}return y(r)}function _(r){switch(typeof r){case"number":case"string":case"undefined":return true;case"boolean":return!r;case"object":if(Array.isArray(r)){return r.every(_)}if(r===null||e(r)){return true}var t=c(r);if(t){var n=t.call(r);var a;if(t!==r.entries){while(!(a=n.next()).done){if(!_(a.value)){return false}}}else{while(!(a=n.next()).done){var i=a.value;if(i){if(!_(i[1])){return false}}}}}else{return false}return true;default:return false}}function S(e,r){if(e==="symbol"){return true}if(r["@@toStringTag"]==="Symbol"){return true}if(typeof Symbol==="function"&&r instanceof Symbol){return true}return false}function P(e){var r=typeof e;if(Array.isArray(e)){return"array"}if(e instanceof RegExp){return"object"}if(S(r,e)){return"symbol"}return r}function C(e){if(typeof e==="undefined"||e===null){return""+e}var r=P(e);if(r==="object"){if(e instanceof Date){return"date"}else if(e instanceof RegExp){return"regexp"}}return r}function I(e){var r=C(e);switch(r){case"array":case"object":return"an "+r;case"boolean":case"date":case"regexp":return"a "+r;default:return r}}function R(e){if(!e.constructor||!e.constructor.name){return p}return e.constructor.name}d.checkPropTypes=s;d.PropTypes=d;return d}}).call(r,t(5))},function(e,r){"use strict";function t(e){return function(){return e}}var n=function e(){};n.thatReturns=t;n.thatReturnsFalse=t(false);n.thatReturnsTrue=t(true);n.thatReturnsNull=t(null);n.thatReturnsThis=function(){return this};n.thatReturnsArgument=function(e){return e};e.exports=n},function(e,r,t){(function(r){"use strict";var t=function e(r){};if(r.env.NODE_ENV!=="production"){t=function e(r){if(r===undefined){throw new Error("invariant requires an error message argument")}}}function n(e,r,n,a,i,u,o,s){t(r);if(!e){var f;if(r===undefined){f=new Error("Minified exception occurred; use the non-minified dev environment "+"for the full error message and additional helpful warnings.")}else{var l=[n,a,i,u,o,s];var c=0;f=new Error(r.replace(/%s/g,(function(){return l[c++]})));f.name="Invariant Violation"}f.framesToPop=1;throw f}}e.exports=n}).call(r,t(5))},function(e,r,t){(function(r){"use strict";var n=t(7);var a=n;if(r.env.NODE_ENV!=="production"){var i=function e(r){for(var t=arguments.length,n=Array(t>1?t-1:0),a=1;a<t;a++){n[a-1]=arguments[a]}var i=0;var u="Warning: "+r.replace(/%s/g,(function(){return n[i++]}));if(typeof console!=="undefined"){console.error(u)}try{throw new Error(u)}catch(o){}};a=function e(r,t){if(t===undefined){throw new Error("`warning(condition, format, ...args)` requires a warning "+"message argument")}if(t.indexOf("Failed Composite propType: ")===0){return}if(!r){for(var n=arguments.length,a=Array(n>2?n-2:0),u=2;u<n;u++){a[u-2]=arguments[u]}i.apply(undefined,[t].concat(a))}}}e.exports=a}).call(r,t(5))},function(e,r){"use strict";var t=Object.getOwnPropertySymbols;var n=Object.prototype.hasOwnProperty;var a=Object.prototype.propertyIsEnumerable;function i(e){if(e===null||e===undefined){throw new TypeError("Object.assign cannot be called with null or undefined")}return Object(e)}function u(){try{if(!Object.assign){return false}var e=new String("abc");e[5]="de";if(Object.getOwnPropertyNames(e)[0]==="5"){return false}var r={};for(var t=0;t<10;t++){r["_"+String.fromCharCode(t)]=t}var n=Object.getOwnPropertyNames(r).map((function(e){return r[e]}));if(n.join("")!=="0123456789"){return false}var a={};"abcdefghijklmnopqrst".split("").forEach((function(e){a[e]=e}));if(Object.keys(Object.assign({},a)).join("")!=="abcdefghijklmnopqrst"){return false}return true}catch(i){return false}}e.exports=u()?Object.assign:function(e,r){var u;var o=i(e);var s;for(var f=1;f<arguments.length;f++){u=Object(arguments[f]);for(var l in u){if(n.call(u,l)){o[l]=u[l]}}if(t){s=t(u);for(var c=0;c<s.length;c++){if(a.call(u,s[c])){o[s[c]]=u[s[c]]}}}}return o}},function(e,r){"use strict";var t="SECRET_DO_NOT_PASS_THIS_OR_YOU_WILL_BE_FIRED";e.exports=t},function(e,r,t){(function(r){"use strict";if(r.env.NODE_ENV!=="production"){var n=t(8);var a=t(9);var i=t(11);var u={}}function o(e,t,o,s,f){if(r.env.NODE_ENV!=="production"){for(var l in e){if(e.hasOwnProperty(l)){var c;try{n(typeof e[l]==="function","%s: %s type `%s` is invalid; it must be a function, usually from "+"the `prop-types` package, but received `%s`.",s||"React class",o,l,typeof e[l]);c=e[l](t,l,s,o,null,i)}catch(d){c=d}a(!c||c instanceof Error,"%s: type specification of %s `%s` is invalid; the type checker "+"function must return `null` or an `Error` but returned a %s. "+"You may have forgotten to pass an argument to the type checker "+"creator (arrayOf, instanceOf, objectOf, oneOf, oneOfType, and "+"shape all require an argument).",s||"React class",o,l,typeof c);if(c instanceof Error&&!(c.message in u)){u[c.message]=true;var p=f?f():"";a(false,"Failed %s type: %s%s",o,c.message,p!=null?p:"")}}}}}e.exports=o}).call(r,t(5))},function(e,r,t){"use strict";var n=t(7);var a=t(8);var i=t(11);e.exports=function(){function e(e,r,t,n,u,o){if(o===i){return}a(false,"Calling PropTypes validators directly is not supported by the `prop-types` package. "+"Use PropTypes.checkPropTypes() to call them. "+"Read more at http://fb.me/use-check-prop-types")}e.isRequired=e;function r(){return e}var t={array:e,bool:e,func:e,number:e,object:e,string:e,symbol:e,any:e,arrayOf:r,element:e,instanceOf:r,node:e,objectOf:r,oneOf:r,oneOfType:r,shape:r,exact:r};t.checkPropTypes=n;t.PropTypes=t;return t}},function(e,r){e.exports=t(44914)},function(e,r){"use strict";var t=function e(r,t){return r===t};function n(e){var r=arguments.length>1&&arguments[1]!==undefined?arguments[1]:t;var n=void 0;var a=[];var i=void 0;var u=false;var o=function e(t,n){return r(t,a[n])};var s=function r(){for(var t=arguments.length,s=Array(t),f=0;f<t;f++){s[f]=arguments[f]}if(u&&n===this&&s.length===a.length&&s.every(o)){return i}u=true;n=this;a=s;i=e.apply(this,s);return i};return s}e.exports=n}])}}]);