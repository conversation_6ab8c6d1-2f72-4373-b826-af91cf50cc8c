#!/usr/bin/env python3
"""
Upload files to RunPod using HTTP/API methods.
Since SSH is having issues, let's try alternative upload methods.
"""

import requests
import base64
import json

def upload_file_content_via_api():
    """Try to upload file content via RunPod API if possible."""
    
    # Read the training script
    try:
        with open('finetune_qwen3.py', 'r', encoding='utf-8') as f:
            training_script = f.read()
        
        with open('requirements.txt', 'r', encoding='utf-8') as f:
            requirements = f.read()
        
        print("✅ Files read successfully!")
        print(f"📄 finetune_qwen3.py: {len(training_script)} characters")
        print(f"📄 requirements.txt: {len(requirements)} characters")
        
        # Create a simple script that can be copy-pasted
        create_upload_script(training_script, requirements)
        
        return True
        
    except FileNotFoundError as e:
        print(f"❌ File not found: {e}")
        return False
    except Exception as e:
        print(f"❌ Error reading files: {e}")
        return False

def create_upload_script(training_script, requirements):
    """Create a script that can be copy-pasted into RunPod terminal."""
    
    upload_script = f'''#!/bin/bash
# RunPod File Upload Script
# Copy and paste this entire script into your RunPod terminal

echo "🚀 Creating training files on RunPod..."

# Create the training script
cat > /workspace/finetune_qwen3.py << 'EOF'
{training_script}
EOF

# Create the requirements file
cat > /workspace/requirements.txt << 'EOF'
{requirements}
EOF

echo "✅ Files created successfully!"
echo "📁 Files in /workspace:"
ls -la /workspace/

echo "🔧 Installing dependencies..."
cd /workspace
pip install -r requirements.txt

echo "🏋️ Ready to start training!"
echo "Run: python finetune_qwen3.py"
'''
    
    # Save the upload script
    with open('runpod_upload_script.sh', 'w', encoding='utf-8') as f:
        f.write(upload_script)
    
    print("\n" + "="*60)
    print("📋 COPY-PASTE SOLUTION CREATED!")
    print("="*60)
    print("✅ Created: runpod_upload_script.sh")
    print("\n🔧 INSTRUCTIONS:")
    print("1. Go to RunPod Console: https://console.runpod.io/")
    print("2. Find your pod 'qwen3-training' and open terminal")
    print("3. Copy the contents of 'runpod_upload_script.sh'")
    print("4. Paste it into the RunPod terminal")
    print("5. Press Enter to execute")
    print("\n📄 The script will:")
    print("   • Create your training files")
    print("   • Install dependencies") 
    print("   • Prepare for training")

def show_manual_commands():
    """Show manual commands that can be run in RunPod terminal."""
    
    print("\n" + "="*60)
    print("📋 MANUAL COMMANDS FOR RUNPOD TERMINAL")
    print("="*60)
    print("If you prefer to do it step by step:")
    print()
    print("1️⃣ Create workspace directory:")
    print("   mkdir -p /workspace")
    print("   cd /workspace")
    print()
    print("2️⃣ Create requirements.txt:")
    print("   cat > requirements.txt << 'EOF'")
    print("   torch>=2.1.0")
    print("   transformers>=4.35.0") 
    print("   datasets>=2.14.0")
    print("   peft>=0.6.0")
    print("   accelerate>=0.24.0")
    print("   bitsandbytes>=0.41.0")
    print("   flash-attn>=2.3.0")
    print("   numpy>=1.24.0")
    print("   scipy>=1.11.0")
    print("   scikit-learn>=1.3.0")
    print("   EOF")
    print()
    print("3️⃣ Install dependencies:")
    print("   pip install -r requirements.txt")
    print()
    print("4️⃣ Create training script:")
    print("   # You'll need to copy the finetune_qwen3.py content manually")
    print("   # Or use the upload script method above")

def main():
    """Main function."""
    print("🔧 RunPod File Upload Helper")
    print("="*40)
    
    success = upload_file_content_via_api()
    
    if success:
        show_manual_commands()
        
        print("\n" + "="*60)
        print("🎯 NEXT STEPS:")
        print("="*60)
        print("1. Access RunPod Console: https://console.runpod.io/")
        print("2. Open terminal for pod 'qwen3-training'")
        print("3. Use the upload script or manual commands above")
        print("4. Start training with: python finetune_qwen3.py")
        
        return True
    else:
        print("❌ Could not read training files")
        return False

if __name__ == "__main__":
    main()
